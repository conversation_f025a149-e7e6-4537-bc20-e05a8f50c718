{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "eslint . --fix"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "lucide-react": "^0.482.0", "next": "15.2.4", "next-international": "^1.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-spinners": "^0.15.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.57", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.2", "tailwindcss": "^4", "typescript": "^5"}}
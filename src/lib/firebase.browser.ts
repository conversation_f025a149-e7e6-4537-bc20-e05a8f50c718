// This file is used to initialize Firebase in the browser.
// It is used in the client-side code to interact with Firebase services.
// Uncomment the code below to use Firebase in the browser.

// import { initializeApp } from "firebase/app";

// import { getFirestore, collection } from "firebase/firestore";
// import { getStorage } from "firebase/storage";

// const clientCredentials = {
//   apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
//   authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
//   projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
//   storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
//   messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
//   appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
//   measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
// };

// const app = initializeApp(clientCredentials);

// const db = getFirestore(app);
// const storage = getStorage(app);

// const projectsCollection = collection(db, "projects");

// export { app, db, storage, projectsCollection };
